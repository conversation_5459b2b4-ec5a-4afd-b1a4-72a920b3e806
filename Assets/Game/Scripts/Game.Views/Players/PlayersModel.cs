using System;
using System.Collections.Generic;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Fusion;
using Game.Core;
using Game.Views.Extensions;
using Game.Views.Storage;
using Modules.Core;
using Modules.Network;
using UnityEngine;
using VContainer;
using VContainer.Unity;

namespace Game.Views.Players
{
    public partial class PlayersModel : ModelBase
    {
        private const int DefaultRadius = 2;

        private readonly PlayersConfig playersConfig;
        private readonly ILocalStorage localStorage;
        private readonly INetworkClient networkClient;
        private readonly IObjectResolver objectResolver;
        private readonly List<PlayerActor> players = new();

        private readonly IAsyncReactiveProperty<PlayerActor> localPlayer = new AsyncReactiveProperty<PlayerActor>(null);
        private readonly IAsyncReactiveProperty<OfflinePlayerActor> offlineLocalPlayer = new AsyncReactiveProperty<OfflinePlayerActor>(null);

        private readonly ISubject<PlayerActor> onLocalPlayerBeforeCreated = new Subject<PlayerActor>();
        private readonly ISubject<PlayerActor> onLocalPlayerBeforeDestroyed = new Subject<PlayerActor>();
        private readonly ISubject<PlayerActor> onPlayerCreated = new Subject<PlayerActor>();
        private readonly ISubject<PlayerActor> onPlayerDestroyed = new Subject<PlayerActor>();

        public IReadOnlyList<PlayerActor> Players => players;
        public IReadOnlyAsyncReactiveProperty<PlayerActor> LocalPlayer => localPlayer;
        public IReadOnlyAsyncReactiveProperty<OfflinePlayerActor> OfflineLocalPlayer => offlineLocalPlayer;
        public IObservable<PlayerActor> OnLocalPlayerBeforeCreated => onLocalPlayerBeforeCreated;
        public IObservable<PlayerActor> OnLocalPlayerBeforeDestroyed => onLocalPlayerBeforeDestroyed;
        public IObservable<PlayerActor> OnPlayerCreated => onPlayerCreated;
        public IObservable<PlayerActor> OnPlayerDestroyed => onPlayerDestroyed;

        private PlayersModel(
            PlayersConfig playersConfig,
            ILocalStorage localStorage,
            INetworkClient networkClient,
            IObjectResolver objectResolver)
        {
            this.playersConfig = playersConfig;
            this.localStorage = localStorage;
            this.networkClient = networkClient;
            this.objectResolver = objectResolver;

            var currentActiveInteractableList = new ActiveInteractableList(localStorage);
            currentActiveInteractableList.Load();
            activeInteractableList = new AsyncReactiveProperty<ActiveInteractableList>(currentActiveInteractableList);

            networkClient.IsConnected.Subscribe(HandleIsConnected).AddTo(DisposeCancellationToken);
            networkClient.OnNetworkActorSpawned.Subscribe(x => HandleNetworkActorSpawned(x.runner, x.actor).Forget()).AddTo(DisposeCancellationToken);
            networkClient.OnNetworkActorDespawned.Subscribe(x => HandleNetworkActorDespawned(x.actor)).AddTo(DisposeCancellationToken);
            networkClient.OnRunnerDestroyed.Subscribe(_ => HandleRunnerDestroyed()).AddTo(DisposeCancellationToken);
        }

        public void CreateLocalPlayer()
        {
            networkClient.Spawn(playersConfig.NetworkPlayerPrefab, Vector3.up, Quaternion.identity, (runner, obj) =>
            {
                var player = obj.GetComponent<PlayerActor>();
                onLocalPlayerBeforeCreated.OnNext(player);
                runner.SetPlayerObject(networkClient.LocalPlayer, obj);
            });
        }

        public void CreateOfflineLocalPlayer()
        {
            if (offlineLocalPlayer.Value)
            {
                return;
            }

            offlineLocalPlayer.Value = objectResolver.Instantiate(playersConfig.OfflinePlayerPrefab);
        }

        public bool TryGetPlayer(PlayerRef player, out PlayerActor playerActor)
        {
            return TryGetPlayer(player.PlayerId, out playerActor);
        }

        public bool TryGetPlayer(int playerId, out PlayerActor playerActor)
        {
            playerActor = players.Find(p => p.Object.StateAuthority.PlayerId == playerId);
            return playerActor;
        }

        public bool TryFindClosestPlayer(Vector3 origin, int radius, out PlayerActor playerActor)
        {
            return TryFindClosestPlayer(origin, radius, null, out playerActor);
        }

        public bool TryFindClosestPlayer(Vector3 origin, int radius, Func<PlayerActor, bool> func, out PlayerActor closestPlayer, int verticalDistanceLimit = int.MaxValue)
        {
            closestPlayer = null;
            var range = float.MaxValue;
            var maxRange = radius * radius;

            foreach (var player in players)
            {
                var verticalDistance = Mathf.Abs(player.transform.position.y - origin.y);
                if (verticalDistance > verticalDistanceLimit)
                {
                    continue;
                }

                var currentRange = (origin - player.transform.position).sqrMagnitude;
                if (currentRange < maxRange && currentRange <= range && (func == null || func(player)))
                {
                    closestPlayer = player;
                    range = currentRange;
                }
            }

            return closestPlayer != null;
        }

        public List<PlayerActor> FindPlayers(Vector3 origin, int radius = DefaultRadius)
        {
            var playerList = new List<PlayerActor>();
            var radiusSqr = radius * radius;

            foreach (var player in players)
            {
                var currentSqrRadius = (origin - player.transform.position).sqrMagnitude;
                if (currentSqrRadius < radiusSqr)
                {
                    playerList.Add(player);
                }
            }

            return playerList;
        }

        public bool IsLocalPlayerInRadius(Vector3 origin, int radius = DefaultRadius)
        {
            if (!LocalPlayer.Value)
            {
                return false;
            }

            var radiusSqr = radius * radius;
            var currentSqrRadius = (origin - LocalPlayer.Value.transform.position).sqrMagnitude;
            return currentSqrRadius < radiusSqr;
        }

        private void TrySetLocalPlayer(PlayerActor player)
        {
            if (!player || !player.HasStateAuthority || localPlayer.Value || localPlayer.Value == player)
            {
                return;
            }

            GameLogger.Player.Debug("Local player created: {0}", player.PlayerId);
            localPlayer.Value = player;
        }

        private void TryClearLocalPlayer(PlayerActor player)
        {
            if (localPlayer.Value != player)
            {
                return;
            }

            ClearLocalPlayer();
        }

        private void ClearLocalPlayer()
        {
            if (!localPlayer.Value)
            {
                return;
            }

            onLocalPlayerBeforeDestroyed.OnNext(localPlayer.Value);
            GameLogger.Player.Debug("Local player destroyed");
            localPlayer.Value = null;
        }

        private async UniTaskVoid HandleNetworkActorSpawned(NetworkRunner runner, NetworkActor actor)
        {
            if (actor is not PlayerActor player)
            {
                return;
            }

            if (runner.GameMode == GameMode.Single)
            {
                await UniTask.Yield(DisposeCancellationToken);
            }
            else
            {
                var cancellationTokens = new[] { networkClient.DisconnectionCancellationToken, DisposeCancellationToken, actor.destroyCancellationToken };
                var resultCancellationToken = CancellationTokenSource.CreateLinkedTokenSource(cancellationTokens).Token;
                await runner.HasPlayerObjectAsync(actor.StateAuthority, resultCancellationToken);
            }

            GameLogger.Player.Debug("Player added: {0}. Count: {1}", player.PlayerId, players.Count + 1);
            players.Add(player);
            TrySetLocalPlayer(player);
            onPlayerCreated.OnNext(player);
        }

        private void HandleNetworkActorDespawned(NetworkActor actor)
        {
            if (actor is not PlayerActor player)
            {
                return;
            }

            GameLogger.Player.Debug("Player removed: {0}. Count: {1}", player.PlayerId, players.Count - 1);
            players.Remove(player);
            TryClearLocalPlayer(player);
            onPlayerDestroyed.OnNext(player);
        }

        private void HandleIsConnected(bool ok)
        {
            if (!ok)
            {
                ignoredPlayers.Clear();
            }
        }

        private void HandleRunnerDestroyed()
        {
            ClearLocalPlayer();
            players.Clear();
        }
    }
}