using System;
using System.Reactive.Subjects;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Views.Items;
using Game.Views.Players;
using Modules.Core;
using Modules.XR;
using UnityEngine;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Views.PlayerUI.Backpack
{
    public class BackpackMenu : Actor
    {
        [SerializeField] private ActivationTrigger activationTrigger;
        [SerializeField] private DeactivationTrigger deactivationTrigger;
        [SerializeField] private BackpackWidget backpackWidget;
        [SerializeField] private Vector3 translationOffset;
        [SerializeField] private Vector3 rotationOffset;

        private readonly ISubject<BackpackActor> onEquip = new Subject<BackpackActor>();
        private readonly ISubject<IXRSelectInteractor> onUnequip = new Subject<IXRSelectInteractor>();

        private IXRInput xrInput;
        private IAudioClient audioClient;

        public IObservable<BackpackActor> OnEquip => onEquip;
        public IObservable<IXRSelectInteractor> OnUnequip => onUnequip;

        [Inject]
        private void Construct(PlayersModel playersModel, IXRInput xrInput, IAudioClient audioClient)
        {
            this.xrInput = xrInput;
            this.audioClient = audioClient;

            playersModel.LocalPlayer.Subscribe(HandleLocalPlayer).AddTo(destroyCancellationToken);
            playersModel.LocalPlayerScale.Subscribe(SetScale).AddTo(destroyCancellationToken);
        }

        public void SetScale(float scale)
        {
            transform.localScale = scale * Vector3.one;
        }

        public void SetPose(Vector3 bodyPosition, Quaternion bodyRotation)
        {
            var position = bodyPosition + bodyRotation * translationOffset;
            var rotation = bodyRotation * Quaternion.Euler(rotationOffset);
            transform.SetPositionAndRotation(position, rotation);
        }

        public void ActivateWidget(string viewCode)
        {
            backpackWidget.Render(viewCode);
            activationTrigger.SetActive(true);
            deactivationTrigger.SetActive(false);
        }

        public void DeactivateWidget(bool isBackpackEquipped)
        {
            if (isBackpackEquipped)
            {
                backpackWidget.SetHoverEndState();
                backpackWidget.SetDefaultView();
            }
            else
            {
                backpackWidget.Hide();
                deactivationTrigger.SetActive(false);
            }

            activationTrigger.SetActive(false);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            if (player == null)
            {
                DeactivateWidget(false);
            }
            else
            {
                var token = CancellationTokenSource.CreateLinkedTokenSource(destroyCancellationToken, player.destroyCancellationToken).Token;
                activationTrigger.OnHoverStarted.Subscribe(HandleActivationTriggerHoverStarted).AddTo(token);
                activationTrigger.OnHoverEnded.Subscribe(HandleActivationTriggerHoverEnded).AddTo(token);
                activationTrigger.OnTriggered.Subscribe(HandleActivationTriggerTriggered).AddTo(token);

                deactivationTrigger.OnHoverStarted.Subscribe(HandleDeactivationHoverStarted).AddTo(token);
                deactivationTrigger.OnHoverEnded.Subscribe(HandleDeactivationHoverEnded).AddTo(token);
                deactivationTrigger.OnTriggered.Subscribe(HandleDeactivationTriggerTriggered).AddTo(token);
            }
        }

        private void HandleActivationTriggerHoverStarted(IXRInteractor interactor)
        {
            backpackWidget.SetHoverStartState();
            xrInput.SendHapticImpulse(interactor, HapticImpulse.ShortDurationLowAmplitude);
        }

        private void HandleActivationTriggerHoverEnded(IXRInteractor interactor)
        {
            backpackWidget.SetHoverEndState();
        }

        private void HandleActivationTriggerTriggered(BackpackActor actor)
        {
            onEquip.OnNext(actor);
            deactivationTrigger.SetActive(true);
            backpackWidget.SetHoverEndState();
            audioClient.Play(AudioKeys.BackpackEquip, backpackWidget.transform.position, destroyCancellationToken);
        }

        private void HandleDeactivationHoverStarted(IXRInteractor interactor)
        {
            backpackWidget.SetHoverStartState();
            xrInput.SendHapticImpulse(interactor, HapticImpulse.ShortDurationLowAmplitude);
        }

        private void HandleDeactivationHoverEnded(IXRInteractor interactor)
        {
            backpackWidget.SetHoverEndState();
        }

        private void HandleDeactivationTriggerTriggered(IXRSelectInteractor interactor)
        {
            onUnequip.OnNext(interactor);
            deactivationTrigger.SetActive(false);
            backpackWidget.SetHoverEndState();
            audioClient.Play(AudioKeys.BackpackDrop, backpackWidget.transform.position, destroyCancellationToken);
        }
    }
}