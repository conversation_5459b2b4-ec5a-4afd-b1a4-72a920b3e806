using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using DG.Tweening;
using Modules.Core;
using Sirenix.OdinInspector;
using UnityEngine;

namespace Game.Views.PlayerEffects
{
    public class SelfDamageEffectView : Actor
    {
        [SerializeField] private Renderer selfRenderer;

        private Tweener effectTweener;
        private CancellationTokenSource effectCancellationTokenSource;

        private void OnDisable()
        {
            effectTweener?.Kill();
            effectCancellationTokenSource.CancelAndDispose();
        }

        public override void SetActive(bool isActive)
        {
            base.SetActive(isActive);
            if (isActive)
            {
                RenderEffect();
            }
        }

        private void RenderEffect()
        {
            effectTweener?.Kill();
            effectTweener = selfRenderer.sharedMaterial.DOFade(1, 0.1f);

            effectCancellationTokenSource.CancelAndDispose();
            effectCancellationTokenSource = new CancellationTokenSource();
            UniTaskAsyncEnumerable
                .Timer(TimeSpan.FromSeconds(1))
                .Subscribe(_ =>
                {
                    effectTweener?.Kill();
                    effectTweener = selfRenderer.sharedMaterial.DOFade(0, 2).OnComplete(Hide);
                })
                .AddTo(effectCancellationTokenSource.Token);
        }

        [Button]
        private void T()
        {
            SetActive(true);
        }
    }
}