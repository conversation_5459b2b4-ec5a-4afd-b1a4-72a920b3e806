using System;
using System.Reactive.Subjects;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Core;
using Game.Views.InteractablesCore;
using Game.Views.Items;
using Modules.Core;
using UnityEngine;
using VContainer;

namespace Game.Views.Ores
{
    public class OreConverterView : Actor
    {
        private const float EnterDelay = 0.4f;

        [SerializeField] private Collider selfCollider;
        [SerializeField] private GameObject vfxObject;

        private readonly ISubject<InteractableActor> onInteractableEntered = new Subject<InteractableActor>();
        private readonly ISubject<BackpackActor> onBackpackEntered = new Subject<BackpackActor>();

        private bool ignoreTrigger;
        private IAudioClient audioClient;

        public IObservable<InteractableActor> OnInteractableEntered => onInteractableEntered;
        public IObservable<BackpackActor> OnBackpackEntered => onBackpackEntered;

        [Inject]
        private void Construct(IAudioClient audioClient)
        {
            this.audioClient = audioClient;
        }

        private void OnDisable()
        {
            ignoreTrigger = false;
        }

        private void OnTriggerStay(Collider other)
        {
            if (ignoreTrigger)
            {
                return;
            }

            if (other.attachedRigidbody.TryGetComponent(out BackpackActor backpack) && backpack.HasStateAuthority && !backpack.IsGrabbed && !backpack.IsEquipped)
            {
                ResolveEnter(() => onBackpackEntered.OnNext(backpack)).Forget();
            }
            else if (other.attachedRigidbody.TryGetComponent(out InteractableActor interactable) && interactable.HasStateAuthority && !interactable.IsGrabbed)
            {
                ResolveEnter(() => onInteractableEntered.OnNext(interactable)).Forget();
            }
        }

        private async UniTaskVoid ResolveEnter(Action callback)
        {
            ignoreTrigger = true;
            selfCollider.enabled = false;
            await UniTask.Delay(TimeSpan.FromSeconds(EnterDelay), cancellationToken: destroyCancellationToken);
            callback?.Invoke();
            await UniTask.Yield(destroyCancellationToken);
            RenderVfx();
            selfCollider.enabled = true;
            ignoreTrigger = false;
        }

        private void RenderVfx()
        {
            vfxObject.SetActive(true);
            audioClient.Play(AudioKeys.ConverterMachine, transform.position, destroyCancellationToken);
            UniTaskAsyncEnumerable
                .Timer(TimeSpan.FromSeconds(3))
                .Subscribe(_ => vfxObject.SetActive(false))
                .AddTo(destroyCancellationToken);
        }
    }
}