using System;
using System.Reactive;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using Game.Views.InteractablesCore;
using Game.Views.Items;
using Game.Views.Players;
using Game.Views.PlayerUI;
using Game.Views.PlayerUI.Backpack;
using Modules.Core;
using UnityEngine.XR.Interaction.Toolkit;
using VContainer;

namespace Game.Controllers.Backpack
{
    public class BackpackController : ControllerBase
    {
        private BackpackMenu backpackMenu;
        private PlayersModel playersModel;

        private PlayerActor LocalPlayer => playersModel.LocalPlayer.Value;
        private bool IsBackpackEquipped => LocalPlayer != null && LocalPlayer.Backpack != null;

        [Inject]
        private void Construct(PlayerMenu playerMenu, PlayersModel playersModel, IInteractableInteractionSubscriber interactableInteractionsSubscriber)
        {
            this.playersModel = playersModel;
            backpackMenu = playerMenu.BackpackMenu;

            interactableInteractionsSubscriber.OnSelectEntered.Subscribe(HandleSelectEntered).AddTo(DisposeCancellationToken);
            interactableInteractionsSubscriber.OnSelectExited.Subscribe(HandleExitEntered).AddTo(DisposeCancellationToken);

            backpackMenu.OnEquip.Subscribe(HandleEquip).AddTo(DisposeCancellationToken);
            backpackMenu.OnUnequip.Subscribe(HandleUnequip).AddTo(DisposeCancellationToken);

            playersModel.LocalPlayer.Where(p => p).Subscribe(HandleLocalPlayer).AddTo(DisposeCancellationToken);
        }

        private void HandleLocalPlayer(PlayerActor player)
        {
            player.OnBeforeDeadLocal.Subscribe(HandleLocalPlayerBeforeDead).AddTo(player);
        }

        private void HandleLocalPlayerBeforeDead(Unit unit)
        {
            LocalPlayer.UnequipBackpack();
            backpackMenu.DeactivateWidget(false);
        }

        private void HandleSelectEntered(InteractionArgs<SelectEnterEventArgs> args)
        {
            if (args.interactable is not BackpackActor backpack)
            {
                return;
            }

            if (!IsBackpackEquipped)
            {
                backpackMenu.ActivateWidget(backpack.InteractableCode);
            }
        }

        private void HandleExitEntered(InteractionArgs<SelectExitEventArgs> args)
        {
            if (args.interactable is not BackpackActor)
            {
                return;
            }

            DeactivateWidgetAsync().Forget();
        }

        private void HandleEquip(BackpackActor backpack)
        {
            if (LocalPlayer != null)
            {
                LocalPlayer.EquipBackpack(backpack);
            }
        }

        private void HandleUnequip(IXRSelectInteractor interactor)
        {
            if (LocalPlayer != null)
            {
                var backpack = LocalPlayer.Backpack;
                LocalPlayer.UnequipBackpack();
                backpack.Grab(interactor);
            }
        }

        private async UniTaskVoid DeactivateWidgetAsync()
        {
            await UniTask.Yield(DisposeCancellationToken);
            backpackMenu.DeactivateWidget(IsBackpackEquipped);
        }
    }
}